from typing import List, Dict, Any
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, Depends, status
from bson import ObjectId
from ..db import get_db
from ..dependencies import require_role
from ..schemas import UserAdmin, UserCreate, PasswordChange, AdminNoteCreate, AdminNoteOut, ClientOut
from ..security import hash_password
from ..utils import to_object_id, convert_object_ids, validate_id_parameter

router = APIRouter(prefix="/admin", tags=["admin"])




@router.get("/users", response_model=List[UserAdmin])
async def list_all_users(admin_user: Dict[str, Any] = Depends(require_role("admin"))):
    """Get all users in the system (admin only)"""
    db = get_db()
    cursor = db.users.find({})
    users = await cursor.to_list(1000)

    result = []
    for user in users:
        safe_user = convert_object_ids(user)
        safe_user["_id"] = str(user["_id"])
        # Convert client_ids to strings if they exist
        if safe_user.get("client_ids"):
            safe_user["client_ids"] = [str(cid) for cid in safe_user["client_ids"]]
        result.append(safe_user)

    return result


@router.post("/users", response_model=UserAdmin)
async def create_user(user_data: UserCreate, admin_user: Dict[str, Any] = Depends(require_role("admin"))):
    """Create a new user (admin only)"""
    db = get_db()

    # Check if user with this email already exists
    existing_user = await db.users.find_one({"email": user_data.email})
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email already exists"
        )

    # Hash the password
    password_hash, salt = hash_password(user_data.password)

    # Prepare user document
    user_doc = {
        "email": user_data.email,
        "full_name": user_data.full_name,
        "role": user_data.role,
        "password_hash": password_hash,
        "salt": salt,
        "client_ids": [ObjectId(cid) for cid in (user_data.client_ids or [])],
        "created_at": datetime.now(timezone.utc)
    }

    # Insert the user
    result = await db.users.insert_one(user_doc)

    # Return the created user
    created_user = await db.users.find_one({"_id": result.inserted_id})
    safe_user = convert_object_ids(created_user)
    safe_user["_id"] = str(created_user["_id"])
    if safe_user.get("client_ids"):
        safe_user["client_ids"] = [str(cid) for cid in safe_user["client_ids"]]

    return safe_user


@router.patch("/users/{user_id}/password")
async def change_user_password(
    user_id: str,
    password_data: PasswordChange,
    admin_user: Dict[str, Any] = Depends(require_role("admin"))
):
    """Change any user's password (admin only)"""
    db = get_db()

    # Convert user_id to ObjectId
    try:
        user_oid = to_object_id(user_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid id format"
        )

    # Check if user exists
    user = await db.users.find_one({"_id": user_oid})
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Hash the new password
    new_password_hash, new_salt = hash_password(password_data.new_password)

    # Update the user's password
    await db.users.update_one(
        {"_id": user_oid},
        {"$set": {
            "password_hash": new_password_hash,
            "salt": new_salt,
            "password_updated_at": datetime.now(timezone.utc)
        }}
    )

    return {"message": f"Password updated for user {user['email']}"}


@router.delete("/users/{user_id}")
async def delete_user(user_id: str, admin_user: Dict[str, Any] = Depends(require_role("admin"))):
    """Delete a user (admin only)"""
    validate_id_parameter(user_id, "User ID")

    db = get_db()

    # Convert user_id to ObjectId
    try:
        user_oid = to_object_id(user_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid id format"
        )

    # Check if user exists
    user = await db.users.find_one({"_id": user_oid})
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Prevent admin from deleting themselves
    if user_oid == admin_user["_id"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own admin account"
        )

    # Delete the user
    await db.users.delete_one({"_id": user_oid})

    return {"message": f"User {user['email']} deleted successfully"}


@router.get("/users/{user_id}", response_model=UserAdmin)
async def get_user_details(user_id: str, admin_user: Dict[str, Any] = Depends(require_role("admin"))):
    """Get detailed information about a specific user (admin only)"""
    validate_id_parameter(user_id, "User ID")

    db = get_db()

    # Convert user_id to ObjectId
    try:
        user_oid = to_object_id(user_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid id format"
        )

    # Find the user
    user = await db.users.find_one({"_id": user_oid})
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Return user details
    safe_user = convert_object_ids(user)
    safe_user["_id"] = str(user["_id"])
    if safe_user.get("client_ids"):
        safe_user["client_ids"] = [str(cid) for cid in safe_user["client_ids"]]


@router.post("/users/{user_id}/notes", response_model=AdminNoteOut)
async def add_user_note(user_id: str, payload: AdminNoteCreate, admin_user: Dict[str, Any] = Depends(require_role("admin"))):
    """Create an admin-authored note attached to a user (admin only)."""
    db = get_db()
    try:
        user_oid = to_object_id(user_id)
    except Exception:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid id format")

    user = await db.users.find_one({"_id": user_oid})
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    note_doc = {
        "user_id": user_oid,
        "body": payload.body,
        "created_at": datetime.now(timezone.utc),
        "created_by": admin_user["_id"],
    }
    result = await db.user_notes.insert_one(note_doc)
    created = await db.user_notes.find_one({"_id": result.inserted_id})
    safe = convert_object_ids(created)
    safe["_id"] = str(created["_id"])
    safe["user_id"] = str(created["user_id"])
    safe["created_by"] = str(created["created_by"])
    return safe


@router.get("/users/{user_id}/notes", response_model=List[AdminNoteOut])
async def list_user_notes(user_id: str, admin_user: Dict[str, Any] = Depends(require_role("admin"))):
    """List admin-authored notes for a user (admin only)."""
    validate_id_parameter(user_id, "User ID")

    db = get_db()
    try:
        user_oid = to_object_id(user_id)
    except Exception:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid id format")

    cursor = db.user_notes.find({"user_id": user_oid}).sort("created_at", -1)
    docs = await cursor.to_list(1000)
    out = []
    for d in docs:
        safe = convert_object_ids(d)
        safe["_id"] = str(d["_id"])
        safe["user_id"] = str(d["user_id"])
        safe["created_by"] = str(d["created_by"])
        out.append(safe)
    return out


@router.patch("/clients/{client_id}/workflow")
async def set_client_workflow(client_id: str, payload: Dict[str, Any], admin_user: Dict[str, Any] = Depends(require_role("admin"))):
    """Set client's workflow step (1-8) and optional label (admin only)."""
    validate_id_parameter(client_id, "Client ID")

    db = get_db()
    try:
        client_oid = to_object_id(client_id)
    except Exception:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid id format")

    client = await db.clients.find_one({"_id": client_oid})
    if not client:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Client not found")

    step = payload.get("workflow_step")
    label = payload.get("workflow_label")
    if step is not None:
        if not isinstance(step, int) or step < 1 or step > 8:
            raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="workflow_step must be int 1-8")
    update: Dict[str, Any] = {}
    if step is not None:
        update["workflow_step"] = step
    if label is not None:
        update["workflow_label"] = label
    if not update:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No fields to update")

    await db.clients.update_one({"_id": client_oid}, {"$set": update})
    doc = await db.clients.find_one({"_id": client_oid})
    safe = convert_object_ids(doc)
    safe["_id"] = str(doc["_id"])
    if safe.get("therapist_id"):
        safe["therapist_id"] = str(doc.get("therapist_id"))
    return safe
