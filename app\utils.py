from typing import Any, Dict
from bson import ObjectId


class InvalidObjectId(ValueError):
    """Raised when a string is not a valid BSON ObjectId."""
    pass


def validate_id_parameter(id_str: str, param_name: str = "ID") -> None:
    """Validate that an ID parameter is not undefined, null, or empty."""
    if id_str.lower() in ("undefined", "null", "") or id_str.strip() == "":
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{param_name} is required and cannot be undefined"
        )


def to_object_id(id_str: str) -> ObjectId:
    if not ObjectId.is_valid(id_str):
        raise InvalidObjectId("Invalid ObjectId")
    return ObjectId(id_str)


def convert_object_ids(doc: Dict[str, Any]) -> Dict[str, Any]:
    out: Dict[str, Any] = {}
    for k, v in doc.items():
        if isinstance(v, ObjectId):
            out[k] = str(v)
        elif isinstance(v, list):
            out[k] = [str(x) if isinstance(x, ObjectId) else x for x in v]
        elif isinstance(v, dict):
            out[k] = convert_object_ids(v)
        else:
            out[k] = v
    return out
