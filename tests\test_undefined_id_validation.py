"""
Test the undefined ID validation functionality.
This ensures that endpoints properly handle 'undefined' and other invalid ID values
that might be sent from frontend applications.
"""

import pytest
from fastapi import HTT<PERSON><PERSON>xception
from app.utils import validate_id_parameter


class TestValidateIdParameter:
    """Test the validate_id_parameter utility function."""
    
    def test_undefined_values_raise_exception(self):
        """Test that undefined-like values raise HTTPException with 400 status."""
        
        invalid_values = [
            "undefined",
            "null", 
            "",
            "   ",  # whitespace only
            "UNDEFINED",  # case insensitive
            "NULL",  # case insensitive
            "\t",  # tab character
            "\n",  # newline character
            "Undefined",  # mixed case
            "nUlL",  # mixed case
        ]
        
        for invalid_value in invalid_values:
            with pytest.raises(HTTPException) as exc_info:
                validate_id_parameter(invalid_value, "Test ID")
            
            assert exc_info.value.status_code == 400
            assert "Test ID is required and cannot be undefined" in exc_info.value.detail
    
    def test_valid_values_pass_validation(self):
        """Test that valid ID values pass validation without raising exceptions."""
        
        valid_values = [
            "507f1f77bcf86cd799439011",  # Valid ObjectId format
            "valid_session_id",
            "123456789",
            "test-id-123",
            "a",  # single character
            "client_123_session_456",
            "user-id-with-dashes",
            "CamelCaseId",
            "snake_case_id",
        ]
        
        for valid_value in valid_values:
            # Should not raise any exception
            validate_id_parameter(valid_value, "Test ID")
    
    def test_custom_parameter_name_in_error_message(self):
        """Test that the parameter name is correctly included in error messages."""
        
        test_cases = [
            ("Session ID", "Session ID is required and cannot be undefined"),
            ("Client ID", "Client ID is required and cannot be undefined"),
            ("User ID", "User ID is required and cannot be undefined"),
            ("Custom Parameter", "Custom Parameter is required and cannot be undefined"),
        ]
        
        for param_name, expected_message in test_cases:
            with pytest.raises(HTTPException) as exc_info:
                validate_id_parameter("undefined", param_name)
            
            assert exc_info.value.status_code == 400
            assert exc_info.value.detail == expected_message


class TestEndpointIntegration:
    """Test that the validation is properly integrated into endpoints."""
    
    def test_sessions_router_imports_validation(self):
        """Test that sessions router can import the validation function."""
        from app.routers.sessions import validate_id_parameter
        assert validate_id_parameter is not None
    
    def test_clients_router_imports_validation(self):
        """Test that clients router can import the validation function."""
        from app.routers.clients import validate_id_parameter
        assert validate_id_parameter is not None
    
    def test_analytics_router_imports_validation(self):
        """Test that analytics router can import the validation function."""
        from app.routers.analytics import validate_id_parameter
        assert validate_id_parameter is not None
    
    def test_admin_router_imports_validation(self):
        """Test that admin router can import the validation function."""
        from app.routers.admin import validate_id_parameter
        assert validate_id_parameter is not None


if __name__ == "__main__":
    # Run the tests directly if this file is executed
    pytest.main([__file__, "-v"])
